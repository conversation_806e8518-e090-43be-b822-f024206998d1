<!-- 紧急呼叫模态窗口 -->
<div class="modal fade" id="emergencyModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h4 class="modal-title"><i class="bi bi-exclamation-triangle-fill me-2"></i> 紧急呼叫</h4>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body p-0">
                <div v-if="!emergencyStatus">
                    <div class="p-4">
                        <h5 class="mb-3">请选择紧急情况类型：</h5>
                        <div class="d-flex justify-content-between gap-3">
                            <div class="emergency-option medical text-center p-4 flex-fill" @click="emergencyType = 'medical'; startEmergencyCall()">
                                <i class="bi bi-heart-pulse fs-1 mb-2"></i>
                                <div>医疗紧急</div>
                            </div>
                            <div class="emergency-option fall text-center p-4 flex-fill" @click="emergencyType = 'fall'; startEmergencyCall()">
                                <img src="/static/images/fall.png" class="fs-1 mb-2" width="60" height="60">
                                <div>跌倒求助</div>
                            </div>
                            <div class="emergency-option help text-center p-4 flex-fill" @click="emergencyType = 'help'; startEmergencyCall()">
                                <i class="bi bi-megaphone-fill fs-1 mb-2"></i>
                                <div>其他求助</div>
                            </div>
                        </div>
                        
                        <div class="alert alert-warning mt-4">
                            <i class="bi bi-info-circle-fill me-2"></i>
                            请注意：点击紧急情况类型后，系统将自动拨打相应的紧急联系人。如非必要，请勿点击。
                        </div>
                    </div>
                    
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    </div>
                </div>
                
                <div v-else>
                    <div class="text-center p-4">
                        <div v-if="emergencyStatus === 'calling'">
                            <!-- 步骤2：呼叫中 -->
                            <div class="text-center mb-4">
                                <div class="emergency-countdown mb-3">
                                    <span>${countdownTimer}$</span>
                                </div>
                                <h5 class="mb-3 fs-3">正在呼叫紧急救援中心...</h5>
                                <p class="text-muted fs-5">请保持通话，不要挂断</p>
                            </div>
                            
                            <!-- 地图组件 -->
                            <div class="emergency-map-container" id="emergencyMap"></div>
                            
                            <div class="location-info">
                                <i class="bi bi-geo-alt-fill"></i>
                                <div>
                                    <p class="mb-1 fs-5" v-if="currentLocation">您的当前位置：${currentLocation.address}$</p>
                                    <p class="mb-0 fs-5" v-else>正在获取您的位置...</p>
                                </div>
                            </div>
                            
                            <!-- 状态进度条 -->
                            <div class="emergency-status-progress">
                                <div class="progress-bar progress-bar-striped progress-bar-animated" style="width: 70%"></div>
                            </div>
                            
                            <div class="text-center mt-4">
                                <button class="btn btn-secondary btn-lg px-4 py-2 mx-2" @click="cancelEmergencyCall">
                                    <i class="bi bi-x-circle me-2"></i> 取消呼叫
                                </button>
                            </div>
                        </div>
                        
                        <div v-else-if="emergencyStatus === 'connected'">
                            <!-- 步骤3：已连接 -->
                            <div class="alert alert-success fs-5 mb-4">
                                <i class="bi bi-check-circle-fill me-2"></i>
                                已成功连接紧急救助中心，救援人员正在赶来
                            </div>
                            
                            <!-- 地图组件 - 使用同一个地图ID -->
                            <div class="emergency-map-container" id="emergencyMap"></div>
                            
                            <div class="location-info">
                                <i class="bi bi-geo-alt-fill"></i>
                                <div>
                                    <p class="mb-1 fs-5" v-if="currentLocation">救援人员将赶往：${currentLocation.address}$</p>
                                    <p class="mb-0 text-success fs-5">预计到达时间：5-10分钟</p>
                                </div>
                            </div>
                        </div>
                        
                        <div v-else>
                            <div class="alert alert-info mb-3">
                                <i class="bi bi-info-circle-fill me-2"></i>
                                ${emergencyStatus}$
                            </div>
                            
                            <div id="emergencyMap" style="height: 200px;" class="mb-3"></div>
                        </div>
                    </div>
                    
                </div>
            </div>
        </div>
    </div>
</div> 